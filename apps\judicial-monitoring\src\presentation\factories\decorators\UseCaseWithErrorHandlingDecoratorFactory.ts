import {
  UseCaseWithErrorHandlingDecorator,
  UseCaseWithErrorHandlingDecoratorParams,
} from '@c4u/shared';
import { environments } from '../../../constants/environments';
import { TeamsAlertProviderFactory } from '../provider/TeamsAlertProviderFactory';
import { connectToMongoDatabase } from '../../../infra/database/mongo/mongoConnection';

export class UseCaseWithErrorHandlingDecoratorFactory extends UseCaseWithErrorHandlingDecorator {
  constructor(params: UseCaseWithErrorHandlingDecoratorParams) {
    super({
      alertData: new TeamsAlertProviderFactory(),
      stage: environments.STAGE(),
      params,
    });
  }

  async build() {
    await connectToMongoDatabase();
    return super.build();
  }
}
