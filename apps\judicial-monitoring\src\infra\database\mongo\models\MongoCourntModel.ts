import { Document, Schema, connection } from 'mongoose';

export type IMongoCourtSchema = {
  id: string;
  name: string;
  nameVariations?: string[];
  region: number;
  office: string;
  register: string;
} & Document;

export const courtSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  nameVariations: {
    type: Array,
    of: String,
    required: false,
  },
  region: {
    type: Number,
    required: true,
  },
  office: {
    type: String,
    required: true,
  },
  register: {
    type: String,
    required: true,
  },
});

courtSchema.set('toJSON', {
  transform(__: any, ret: any, _: any) {
    ret.id = ret._id;
    delete ret.__v;
    delete ret._id;
  },
});

export const MongoCourtModel = connection.model<IMongoCourtSchema>(
  'court',
  courtSchema,
);
