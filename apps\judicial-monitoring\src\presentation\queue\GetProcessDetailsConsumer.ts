import { SQSAdapter, SQSEvent } from '@c4u/shared';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { GetProcessUpdatesUseCaseFactory } from '../factories/useCases/GetProcessUpdatesUseCaseFactory';

export const handler = async (event: SQSEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    data: event,
    parseData: SQSAdapter.toData,
    schema: {
      processesNumber: yup.string().required(),
    },
    isSQS: true,
    callback: async data =>
      new GetProcessUpdatesUseCaseFactory().execute({
        processNumber: data.processesNumber,
      }),
  }).build();
};
