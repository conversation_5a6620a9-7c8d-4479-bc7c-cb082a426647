import { IUseCase } from '@c4u/shared';
import { ProcessEntity } from '../entities/ProcessEntity';
import { IGetProcessByIdData } from '../data/process/IGetProcessByIdData';
import { ProcessNotFoundError } from '../errors/ProcessNotFoundError';

export type Params = {
  processId: string;
};

export type Result = ProcessEntity;

export abstract class GetProcessByIdUseCase
  implements IUseCase<Params, Result>
{
  constructor(
    private readonly dependencies: {
      getProcessById: IGetProcessByIdData;
    },
  ) {}

  async execute(params: Params): Promise<Result> {
    const process = await this.dependencies.getProcessById.getProcessById(
      params.processId,
    );

    if (!process) {
      throw new ProcessNotFoundError();
    }

    return process;
  }
}
