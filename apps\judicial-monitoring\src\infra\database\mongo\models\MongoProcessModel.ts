import { Document, Schema, connection } from 'mongoose';
import {
  ProcessEntityGeneralData,
  ProcessEntityUpdate,
} from '../../../../domain/entities/ProcessEntity';
import { CourtEntity } from '../../../../domain/entities/CourntEntity';
import { courtSchema } from './MongoCourntModel';

export type IMongoProcessSchema = {
  id: string;
  externalId: string;
  court: CourtEntity;
  processYear: string;
  processNumber: string;
  origin: string;
  url?: string;
  generalData?: ProcessEntityGeneralData;
  updates?: ProcessEntityUpdate[];
  createdAt: Date;
  updatedAt: Date;
} & Document;

export const processSchema = new Schema(
  {
    externalId: {
      type: String,
      required: true,
    },
    court: courtSchema,
    processYear: {
      type: String,
      required: true,
    },
    processNumber: {
      type: String,
      required: true,
    },
    origin: {
      type: String,
      required: true,
    },
    url: {
      type: String,
    },
    generalData: {
      type: Object,
      required: false,
    },
    updates: {
      type: Array,
    },
  },
  {
    timestamps: true,
  },
);

processSchema.set('toJSON', {
  transform(__: any, ret: any, _: any) {
    ret.id = ret._id;
    delete ret.__v;
    delete ret._id;
  },
});

export const MongoProcessModel = connection.model<IMongoProcessSchema>(
  'process',
  processSchema,
);
