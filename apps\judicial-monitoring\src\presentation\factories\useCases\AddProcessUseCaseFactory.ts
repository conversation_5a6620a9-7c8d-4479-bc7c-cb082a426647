import { AddProcessUseCase } from '../../../domain/useCases/AddProcessUseCase';
import { MongoCourtRepository } from '../../../infra/database/mongo/repositories/MongoCourtRepository';
import { MongoProcessRepository } from '../../../infra/database/mongo/repositories/MongoProcessRepository';
import { SQSNotifierLib } from '../../../infra/libs/sqs/SQSNotifierLib';

export class AddProcessUseCaseFactory extends AddProcessUseCase {
  constructor() {
    const processRepository = new MongoProcessRepository();
    const courtRepository = new MongoCourtRepository();
    const notifier = new SQSNotifierLib();

    const dependencies = {
      getProcessByNumber: processRepository,
      getCourtByName: courtRepository,
      saveProcess: processRepository,
      notifier,
    };
    super(dependencies);
  }
}
