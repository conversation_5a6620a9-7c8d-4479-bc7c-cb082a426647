import { PublishCommand, SNSClient } from '@aws-sdk/client-sns';
import { StageEnum } from '@c4u/shared';
import {
  INotifierData,
  NotifierOperationEnum,
  Params,
} from '../../../domain/data/notifier/INotifierData';
import { environments } from '../../../constants/environments';

const snsClient = new SNSClient({
  region: 'us-east-1',
});

export class SNSNotifierLib implements INotifierData {
  private arnByOperation = {
    [NotifierOperationEnum.UPDATED_PROCESS]: `arn:aws:sns:us-east-1:${environments.AWS_ACCOUNT_ID}:judicial-monitoring-${environments.STAGE()}-updated-process`,
  };

  async send({ data, operation }: Params): Promise<void> {
    if (environments.STAGE() !== StageEnum.DEV) {
      const existsOperation = Object.keys(this.arnByOperation).includes(
        operation,
      );
      if (!existsOperation) {
        throw new Error(`SNSNotifierLib: Operation ${operation} not found`);
      }

      const params = {
        Message: JSON.stringify(data),
        TopicArn:
          this.arnByOperation[operation as keyof typeof this.arnByOperation],
      };
      const command = new PublishCommand(params);
      await snsClient.send(command);
    }
  }
}
