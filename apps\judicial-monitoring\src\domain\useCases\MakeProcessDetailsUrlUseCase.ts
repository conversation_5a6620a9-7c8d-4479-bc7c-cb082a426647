import { IUseCase } from '@c4u/shared';
import { IMakeProcessDetailsUrlData } from '../data/process/IMakeProcessDetailsUrlData';
import { IGetProcessByNumberData } from '../data/process/IGetProcessByNumberData';
import { ProcessNotFoundError } from '../errors/ProcessNotFoundError';
import { IUpdateProcessData } from '../data/process/IUpdateProcessData';
import { ProcessUrlIsEmptyError } from '../errors/ProcessUrlIsEmptyError';

export type Request = {
  processNumber: string;
};

export abstract class MakeProcessDetailsUrlUseCase
  implements IUseCase<Request, void>
{
  constructor(
    private readonly dependencies: {
      makeProcessDetailsUrl: IMakeProcessDetailsUrlData;
      getProcessByNumber: IGetProcessByNumberData;
      updateProcess: IUpdateProcessData;
    },
  ) {}

  async execute({ processNumber }: Request): Promise<void> {
    const process =
      await this.dependencies.getProcessByNumber.getProcessByNumber(
        processNumber,
      );

    if (!process) {
      throw new ProcessNotFoundError();
    }

    const processUrl = await this.dependencies.makeProcessDetailsUrl.make({
      processNumber,
      processYear: process.processYear,
      region: process.court.region,
      office: process.court.office,
      register: process.court.register,
    });

    if (!processUrl) {
      throw new ProcessUrlIsEmptyError();
    }

    await this.dependencies.updateProcess.updateProcess({
      ...process,
      url: processUrl,
    });
  }
}
