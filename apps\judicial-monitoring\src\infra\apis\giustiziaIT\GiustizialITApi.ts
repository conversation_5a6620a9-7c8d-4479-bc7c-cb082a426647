import * as cheerio from 'cheerio';
import { GiustiziaItBaseAPI } from './GiustizialITBaseAPI';
import * as IMakeProcessDetailsUrlData from '../../../domain/data/process/IMakeProcessDetailsUrlData';
import * as IGetProcessUpdatesWithUrlData from '../../../domain/data/process/IGetProcessUpdatesWithUrlData';

export class GiustiziaItApi
  extends GiustiziaItBaseAPI
  implements
    IMakeProcessDetailsUrlData.IMakeProcessDetailsUrlData,
    IGetProcessUpdatesWithUrlData.IGetProcessUpdatesWithUrlData
{
  async make({
    processNumber,
    processYear,
    region,
    office,
    register,
  }: IMakeProcessDetailsUrlData.Params): Promise<IMakeProcessDetailsUrlData.Result> {
    const formData = new URLSearchParams();
    formData.append('numeroRuoloGen', processNumber);
    formData.append('annoRuoloGen', processYear);
    formData.append('registroRicerca', register);
    formData.append('searchType', 'consp_ruolo_gen');
    formData.append('regioneRicerca', region.toString());
    formData.append('ufficioRicerca', office.toString());
    formData.append('captchaIsValid', 'true');

    const response = await this.api.post('/PST/it/pst_2_6_1.wp', formData);

    const $ = cheerio.load(response.data);
    const url = $('table tbody tr td a').attr('href');

    return url ?? null;
  }

  async getProcessUpdatesWithUrl(
    data: IGetProcessUpdatesWithUrlData.Params,
  ): Promise<IGetProcessUpdatesWithUrlData.Result> {
    const response = await this.api.get(data.url);

    const $ = cheerio.load(response.data);
    const categories = $('.dettaglioRuoloGenerale')
      .toArray()
      .map(item => $(item).html());

    const rows = categories
      .map(category => {
        if (category) {
          const newItem = cheerio.load(category);
          const li = newItem('li').toArray();
          const liText = li.map(item => $(item).text());

          return liText.map(text => text.trim().replace(/\s+/g, ' '));
        }
        return null;
      })
      .filter(row => row !== null);

    if (rows.length < 3) {
      throw new Error('Invalid response on GiustiziaItApi');
    }

    const updates = rows[2];

    const keysToGetValues = {
      'Numero ruolo generale:': 'processNumber',
      'Ritualità:': 'rite',
      'Oggetto del fascicolo:': 'processObject',
      'Giudice:': 'judge',
      'Sezione:': 'seccion',
      'Data di iscrizione a ruolo:': 'registrationDate',
      'Data citazione:': 'citationDate',
      'Data prossima udienza:': 'nextHearingDate',
      'Sentenza:': 'sentenceNumber',
      'Decreto ingiuntivo:': 'injuctionDecreeNumber',
      'Stato del fascicolo:': 'processStatus',
      'Codice Vestanet:': 'vestanetCode',
      'Nazione di provenienza della parte:': 'partOriginCountry',
      'Numero e anno provvedimento impugnato:': 'contestedProvisionDate',
      'Data notifica provvedimento:': 'provisionContestionDate',
      'Motivazioni della richiesta sospensiva:': 'suspensionRequestReason',
    };

    const generalData = rows[0].reduce(
      (acc, item) => {
        const key = Object.keys(keysToGetValues).find(key =>
          item.includes(key),
        );
        const property = keysToGetValues[key as keyof typeof keysToGetValues];
        acc[property] = item.replace(key ?? '', '').trim();
        return acc;
      },
      {} as Record<string, string>,
    );

    return {
      generalData: { ...generalData, htmlData: response.data } as any,
      updates: updates.map(item => {
        const spliteData = item.split(' - ');
        return {
          date: spliteData[0],
          text: spliteData[1],
        };
      }),
    };
  }
}
