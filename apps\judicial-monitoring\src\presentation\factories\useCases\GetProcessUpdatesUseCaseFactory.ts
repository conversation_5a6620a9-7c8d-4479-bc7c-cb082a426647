import { GetProcessUpdatesUseCase } from '../../../domain/useCases/GetProcessUpdatesUseCase';
import { GiustiziaItApi } from '../../../infra/apis/giustiziaIT/GiustizialITApi';
import { MongoProcessRepository } from '../../../infra/database/mongo/repositories/MongoProcessRepository';

export class GetProcessUpdatesUseCaseFactory extends GetProcessUpdatesUseCase {
  constructor() {
    const getProcessUpdatesWithUrl = new GiustiziaItApi();
    const mongoProcessRepository = new MongoProcessRepository();

    const dependencies = {
      getProcessUpdatesWithUrl,
      getProcessByNumber: mongoProcessRepository,
      updateProcess: mongoProcessRepository,
    };

    super(dependencies);
  }
}
