import { ProcessOriginEnum } from '../../constants/processOrigin';
import { CourtEntity } from './CourntEntity';

export type ProcessEntityGeneralData = {
  processNumber?: string;
  rite?: string;
  processObject?: string;
  judge?: string;
  seccion?: string;
  registrationDate?: string;
  citationDate?: string;
  nextHearingDate?: string;
  sentenceNumber?: string;
  injuctionDecreeNumber?: string;
  processStatus?: string;
  vestanetCode?: string;
  partOriginCountry?: string;
  contestedProvisionDate?: string;
  provisionContestionDate?: string;
  suspensionRequestReason?: string;
  htmlData?: string;
};

export type ProcessEntityUpdate = {
  date: string;
  text: string;
};

export type ProcessEntity = {
  id: string;
  externalId: string;
  court: CourtEntity;
  processYear: string;
  processNumber: string;
  origin: ProcessOriginEnum;
  url?: string;
  generalData?: ProcessEntityGeneralData;
  updates?: ProcessEntityUpdate[];
  createdAt: Date;
  updatedAt: Date;
};
