import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';

import { StageEnum, SQSEvent } from '@c4u/shared';
import {
  INotifierData,
  NotifierOperationEnum,
  Params,
} from '../../../domain/data/notifier/INotifierData';
import { environments } from '../../../constants/environments';
import { handler as addedNewProcessConsumer } from '../../../presentation/queue/AddedNewProcessConsumer';
import { handler as processBatchUpdateConsumer } from '../../../presentation/queue/ProcessBatchUpdateConsumer';
import { handler as getProcessDetailsConsumer } from '../../../presentation/queue/GetProcessDetailsConsumer';

const sqsClient = new SQSClient({
  region: 'us-east-1',
});

export class SQSNotifierLib implements INotifierData {
  private urlByOperation = {
    [NotifierOperationEnum.ADDED_NEW_PROCESS]: `https://sqs.us-east-1.amazonaws.com/${environments.AWS_ACCOUNT_ID}/judicial-monitoring-${environments.STAGE()}-added-new-process`,
    [NotifierOperationEnum.PROCESS_BATCH_UPDATE]: `https://sqs.us-east-1.amazonaws.com/${environments.AWS_ACCOUNT_ID}/judicial-monitoring-${environments.STAGE()}-process-batch-update`,
    [NotifierOperationEnum.GET_PROCESS_DETAILS]: `https://sqs.us-east-1.amazonaws.com/${environments.AWS_ACCOUNT_ID}/judicial-monitoring-${environments.STAGE()}-get-process-details`,
  };

  private handlerByOperation = {
    [NotifierOperationEnum.ADDED_NEW_PROCESS]: addedNewProcessConsumer,
    [NotifierOperationEnum.PROCESS_BATCH_UPDATE]: processBatchUpdateConsumer,
    [NotifierOperationEnum.GET_PROCESS_DETAILS]: getProcessDetailsConsumer,
  };

  async send({ data, operation }: Params): Promise<void> {
    if (environments.STAGE() !== StageEnum.DEV) {
      const existsOperation = Object.keys(this.urlByOperation).includes(
        operation,
      );
      if (!existsOperation) {
        throw new Error(`SQSNotifierLib: Operation ${operation} not found`);
      }

      const params = {
        QueueUrl:
          this.urlByOperation[operation as keyof typeof this.urlByOperation],
        MessageBody: JSON.stringify(data),
      };
      const command = new SendMessageCommand(params);
      await sqsClient.send(command);
    } else {
      await this.callLocalHander({ data, operation });
    }
  }

  private async callLocalHander({ data, operation }: Params): Promise<void> {
    const handler = Object.entries(this.handlerByOperation).find(
      ([key]) => key === operation,
    )?.[1];

    if (handler) {
      const sqsData: SQSEvent = {
        Records: [
          {
            body: JSON.stringify(data),
          },
        ],
      };
      await handler(sqsData);
    }
  }
}
