service: judicial-monitoring
frameworkVersion: '3'

useDotenv: true

provider:
  name: aws
  runtime: nodejs20.x
  deploymentBucket:
    name: ${env:BUCKET_NAME}
  region: 'us-east-1'
  apiGateway:
    restApiId: ${env:REST_API_ID}
    restApiRootResourceId: ${env:REST_API_ROOT_RESOURCE_ID}
  environment:
    AUTHORIZER_ID: ${env:AUTHORIZER_ID}
  vpc:
    securityGroupIds:
      - ${env:SECURITY_GROUP}
    subnetIds:
      - ${env:SUBNET_FIRST}
      - ${env:SUBNET_SECOND}
  iam:
    role:
      statements:
        - Effect: 'Allow'
          Action:
            - 'sqs:Publish'
          Resource: '*'
        - Effect: 'Allow'
          Action:
            - 'sns:Publish'
          Resource: '*'

plugins:
  - serverless-webpack
  - serverless-plugin-datadog
  - serverless-plugin-log-retention
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-lift

custom:
  shortStage:
    dev: dev
    homol: homol
    prod: prod
    'new-homol': homol
    'new-prod': prod
  logRetentionInDays: 1
  serverless-offline:
    host: 0.0.0.0
    httpPort: 4000
  datadog:
    apiKey: ${env:DD_API_KEY}
    env: ${self:custom.shortStage.${env:STAGE}}
    service: platform-lambda-${self:custom.shortStage.${env:STAGE}}
    captureLambdaPayload: true
  webpack:
    webpackConfig: './webpack.config.js'
    packager: 'npm'
    includeModules:
      forceExclude:
        - '@aws-sdk/client-sqs'
        - '@aws-sdk/client-sns'
        - 'puppeteer'
        - '@sparticuz/chromium'

package:
  individually: true

constructs:
  added-new-process:
    type: queue
    maxRetries: 3
    batchSize: 1
    worker:
      logRetentionInDays: 1
      handler: src/presentation/queue/AddedNewProcessConsumer.handler
      memorySize: 256
      timeout: 120
  process-batch-update:
    type: queue
    maxRetries: 3
    batchSize: 1
    worker:
      logRetentionInDays: 1
      handler: src/presentation/queue/ProcessBatchUpdateConsumer.handler
      memorySize: 128
      timeout: 30
  get-process-details:
    type: queue
    maxRetries: 3
    batchSize: 1
    maxConcurrency: 50
    worker:
      logRetentionInDays: 1
      handler: src/presentation/queue/GetProcessDetailsConsumer.handler
      memorySize: 256
      timeout: 120

functions:
  addNewProcess:
    handler: src/presentation/http/AddNewProcessHandler.handler
    memorySize: 128
    timeout: 30
    events:
      - http:
          path: /process
          method: post
          cors: true
          authorizer:
            type: TOKEN
            authorizerId: ${self:provider.environment.AUTHORIZER_ID}
  startMonitoring:
    handler: src/presentation/cron/StartMonitoringCron.handler
    memorySize: 128
    timeout: 30
    events:
      - http:
          path: /manually-start-monitoring
          method: post
          cors: true
          authorizer:
            type: TOKEN
            authorizerId: ${self:provider.environment.AUTHORIZER_ID}
      - schedule: cron(0 14 ? * MON-FRI *)
