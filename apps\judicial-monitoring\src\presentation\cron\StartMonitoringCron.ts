import { NotifierOperationEnum } from '../../domain/data/notifier/INotifierData';
import { connectToMongoDatabase } from '../../infra/database/mongo/mongoConnection';
import { MongoProcessRepository } from '../../infra/database/mongo/repositories/MongoProcessRepository';
import { SQSNotifierLib } from '../../infra/libs/sqs/SQSNotifierLib';

export const handler = async () => {
  await connectToMongoDatabase();
  console.log('StartMonitoringCron started at ', new Date().toISOString());

  const processRepository = new MongoProcessRepository();
  const notifier = new SQSNotifierLib();

  const count = await processRepository.listProcessCount({
    filters: {
      hasUrl: true,
    },
  });

  console.log('Total processes with url to process: ', count);

  const batchSize = 100;

  const batchs: { page: number; perPage: number }[] = [];

  for (let i = 0; i < count; i += batchSize) {
    batchs.push({ page: i / batchSize, perPage: batchSize });
  }

  console.log('Total batchs to process: ', batchs.length);

  await Promise.all(
    batchs.map(async batch => {
      notifier.send({
        operation: NotifierOperationEnum.PROCESS_BATCH_UPDATE,
        data: batch,
      });
    }),
  );

  console.log('StartMonitoringCron finished at ', new Date().toISOString());
};
