import { SQSAdapter, SQSEvent } from '@c4u/shared';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { MakeProcessDetailsUrlUseCaseFactory } from '../factories/useCases/MakeProcessDetailsUrlUseCaseFactory';

export const handler = async (event: SQSEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    isSQS: true,
    parseData: SQSAdapter.toData,
    data: event,
    schema: {
      processNumber: yup.string().required(),
    },
    callback: async data =>
      new MakeProcessDetailsUrlUseCaseFactory().execute({
        processNumber: data.processNumber,
      }),
  }).build();
};
