# Judicial Monitoring System

Sistema de monitoramento de processos judiciais que automatiza a coleta e acompanhamento de atualizações de processos no sistema judiciário italiano (Giustizia.it).

## 📋 Visão Geral

Este sistema monitora processos judiciais automaticamente, coletando informações e atualizações de tribunais italianos. Utiliza uma arquitetura serverless na AWS com Lambda Functions, SQS para processamento assíncrono e MongoDB para persistência de dados.

### Principais Funcionalidades

- **Adição de Processos**: Cadastro de novos processos para monitoramento
- **Monitoramento Automático**: Verificação periódica de atualizações nos processos
- **Coleta de Dados**: Extração de informações detalhadas dos processos
- **Processamento em Lote**: Processamento eficiente de grandes volumes de processos
- **Notificações**: Sistema de notificações via SQS para processamento assíncrono

## 🏗️ Arquitetura

### Componentes Principais

- **Lambda Functions**: Processamento serverless das operações
- **SQS Queues**: Processamento assíncrono e desacoplado
- **MongoDB**: Banco de dados para persistência
- **API Gateway**: Interface REST para interação externa
- **Puppeteer**: Web scraping para coleta de dados dos tribunais

### Fluxo de Dados

1. **Adição de Processo** → Validação → Armazenamento → Notificação SQS
2. **Cron de Monitoramento** → Busca processos → Cria lotes → Envia para SQS
3. **Processamento de Lote** → Processa cada processo → Coleta atualizações
4. **Coleta de Detalhes** → Acessa tribunal → Extrai dados → Atualiza banco

## 🚀 Tecnologias

- **Runtime**: Node.js 20.x
- **Framework**: Serverless Framework
- **Linguagem**: TypeScript
- **Banco de Dados**: MongoDB
- **Cloud Provider**: AWS
- **Web Scraping**: Puppeteer + Cheerio
- **Validação**: Yup
- **HTTP Client**: Axios

## 📁 Estrutura do Projeto

```
src/
├── constants/           # Constantes e enums
├── domain/             # Lógica de negócio
│   ├── data/           # Interfaces de dados
│   ├── entities/       # Entidades do domínio
│   ├── errors/         # Erros customizados
│   └── useCases/       # Casos de uso
├── infra/              # Infraestrutura
│   ├── apis/           # Integrações externas
│   ├── database/       # Acesso a dados
│   └── libs/           # Bibliotecas auxiliares
└── presentation/       # Camada de apresentação
    ├── cron/           # Jobs agendados
    ├── factories/      # Factories para injeção de dependência
    ├── http/           # Handlers HTTP
    └── queue/          # Consumers SQS
```

## 🔧 Configuração

### Pré-requisitos

- Node.js 20.x
- NPM ou Yarn
- AWS CLI configurado
- MongoDB

### Variáveis de Ambiente

```env
STAGE=dev|homol|prod
MONGO_URL=mongodb://localhost:27017/judicial-monitoring
AWS_ACCOUNT_ID=************
GIUSTIZIA_IT_BASE_URL=https://www.giustizia.it
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/...
BUCKET_NAME=deployment-bucket
REST_API_ID=api-gateway-id
REST_API_ROOT_RESOURCE_ID=root-resource-id
AUTHORIZER_ID=authorizer-id
SECURITY_GROUP=sg-xxxxxxxxx
SUBNET_FIRST=subnet-xxxxxxxxx
SUBNET_SECOND=subnet-yyyyyyyyy
```

### Instalação

```bash
# Instalar dependências
npm install

# Carregar variáveis de ambiente
npm run load:env

# Executar localmente
npm run start

# Deploy
serverless deploy --stage dev
```

## 📡 API Endpoints

### POST /process
Adiciona um novo processo para monitoramento.

**Request Body:**
```json
{
  "processNumber": "12345",
  "processYear": "2024",
  "court": "Tribunale di Roma",
  "externalId": "ext-123",
  "origin": "PLATFORM"
}
```

### POST /manually-start-monitoring
Inicia manualmente o processo de monitoramento.

## ⚙️ Filas SQS

### added-new-process
Processa novos processos adicionados, gerando URLs de detalhes.

### process-batch-update
Processa lotes de processos para verificação de atualizações.

### get-process-details
Coleta detalhes específicos de um processo no tribunal.

## 🕐 Jobs Agendados

### StartMonitoringCron
- **Frequência**: Segunda a sexta, 14:00 UTC
- **Função**: Inicia o processo de monitoramento de todos os processos ativos
- **Processamento**: Divide processos em lotes de 100 para processamento eficiente

## 📊 Entidades de Dados

### ProcessEntity
```typescript
{
  id: string;
  externalId: string;
  court: CourtEntity;
  processYear: string;
  processNumber: string;
  origin: 'MAESTRO' | 'PLATFORM';
  url?: string;
  generalData?: ProcessEntityGeneralData;
  updates?: ProcessEntityUpdate[];
  createdAt: Date;
  updatedAt: Date;
}
```

### CourtEntity
```typescript
{
  id: string;
  name: string;
  nameVariations?: string[];
  region: number;
  office: string;
  register: string;
}
```

## 🔍 Monitoramento

O sistema coleta as seguintes informações dos processos:

- **Dados Gerais**: Número do processo, ritualidade, objeto, juiz, seção
- **Datas**: Inscrição, citação, próxima audiência
- **Status**: Estado do fascículo, sentenças, decretos
- **Atualizações**: Histórico cronológico de movimentações

## 🛠️ Desenvolvimento

### Scripts Disponíveis

```bash
npm run lint          # Verificação de código
npm run start         # Execução local com hot reload
npm run dev           # Execução local simples
npm run load:env      # Carregamento de variáveis de ambiente
```

### Testes

Para garantir a qualidade do código, recomenda-se escrever testes unitários para os casos de uso e integrações.

## 🚀 Deploy

O sistema utiliza o Serverless Framework para deploy na AWS:

```bash
# Deploy para desenvolvimento
serverless deploy --stage dev

# Deploy para homologação
serverless deploy --stage homol

# Deploy para produção
serverless deploy --stage prod
```

## 📈 Monitoramento e Logs

- **CloudWatch**: Logs centralizados das Lambda Functions
- **Datadog**: Métricas e monitoramento avançado
- **Retenção de Logs**: 1 dia para desenvolvimento

## 🔒 Segurança

- **Autorização**: Token-based authentication via API Gateway
- **VPC**: Execução em VPC privada com security groups
- **IAM**: Permissões mínimas necessárias para SQS e SNS

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto é propriedade da Cidadania4u e está sob licença proprietária.

---

**Desenvolvido por**: Equipe Cidadania4u  
**Versão**: 1.0.0  
**Última Atualização**: 2024
