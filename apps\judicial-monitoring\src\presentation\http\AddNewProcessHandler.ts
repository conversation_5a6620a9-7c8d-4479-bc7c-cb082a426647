import { APIGatewayEvent } from 'aws-lambda';
import { APIGatewayAdapter } from '@c4u/shared';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { ProcessOriginEnum } from '../../constants/processOrigin';
import { AddProcessUseCaseFactory } from '../factories/useCases/AddProcessUseCaseFactory';

export const handler = async (event: APIGatewayEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    data: event,
    parseData: APIGatewayAdapter.toData,
    schema: {
      processNumber: yup.string().required(),
      processYear: yup.string().required(),
      court: yup.string().required(),
      externalId: yup.string().required(),
      origin: yup.string().oneOf(Object.values(ProcessOriginEnum)),
    },
    callback: async data => new AddProcessUseCaseFactory().execute(data),
  }).build();
};
