import { IUseCase } from '@c4u/shared/dist/core/IUseCase';
import { IGetProcessUpdatesWithUrlData } from '../data/process/IGetProcessUpdatesWithUrlData';
import { IGetProcessByNumberData } from '../data/process/IGetProcessByNumberData';
import { ProcessNotFoundError } from '../errors/ProcessNotFoundError';
import { IUpdateProcessData } from '../data/process/IUpdateProcessData';
import { ProcessEntityGeneralData } from '../entities/ProcessEntity';
import {
  INotifierData,
  NotifierOperationEnum,
} from '../data/notifier/INotifierData';

export type Params = {
  processNumber: string;
};

export abstract class GetProcessUpdatesUseCase
  implements IUseCase<Params, void>
{
  constructor(
    private readonly dependencies: {
      getProcessUpdatesWithUrl: IGetProcessUpdatesWithUrlData;
      getProcessByNumber: IGetProcessByNumberData;
      updateProcess: IUpdateProcessData;
      notifier: INotifierData;
    },
  ) {}

  async execute({ processNumber }: Params): Promise<void> {
    const process =
      await this.dependencies.getProcessByNumber.getProcessByNumber(
        processNumber,
      );

    if (!process?.url) {
      throw new ProcessNotFoundError();
    }

    const updatedProcessData =
      await this.dependencies.getProcessUpdatesWithUrl.getProcessUpdatesWithUrl(
        {
          url: process.url,
        },
      );

    let hasChanges = false;

    Object.entries(updatedProcessData.generalData).forEach(([key, value]) => {
      if (
        value !== process.generalData?.[key as keyof ProcessEntityGeneralData]
      ) {
        hasChanges = true;
      }
    });

    updatedProcessData.updates?.forEach(update => {
      const containsElement = process.updates?.some(
        item => item.date === update.date && item.text === update.text,
      );

      if (!containsElement) {
        hasChanges = true;
      }
    });

    if (!hasChanges) {
      return;
    }

    const updatedProcess = await this.dependencies.updateProcess.updateProcess({
      id: process.id,
      generalData: updatedProcessData.generalData,
      updates: updatedProcessData.updates,
    });

    await this.dependencies.notifier.send({
      operation: NotifierOperationEnum.UPDATED_PROCESS,
      data: {
        processId: updatedProcess?.id,
      },
    });
  }
}
