import { MakeProcessDetailsUrlUseCase } from '../../../domain/useCases/MakeProcessDetailsUrlUseCase';
import { GiustiziaItApi } from '../../../infra/apis/giustiziaIT/GiustizialITApi';
import { MongoProcessRepository } from '../../../infra/database/mongo/repositories/MongoProcessRepository';

export class MakeProcessDetailsUrlUseCaseFactory extends MakeProcessDetailsUrlUseCase {
  constructor() {
    const processRepository = new MongoProcessRepository();
    const giustiziaItApi = new GiustiziaItApi();

    const dependencies = {
      makeProcessDetailsUrl: giustiziaItApi,
      getProcessByNumber: processRepository,
      updateProcess: processRepository,
    };
    super(dependencies);
  }
}
