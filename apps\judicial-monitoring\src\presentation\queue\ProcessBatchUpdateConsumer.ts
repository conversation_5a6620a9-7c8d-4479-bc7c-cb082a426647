import { SQSAdapter, SQSEvent } from '@c4u/shared';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { SQSNotifierLib } from '../../infra/libs/sqs/SQSNotifierLib';
import { MongoProcessRepository } from '../../infra/database/mongo/repositories/MongoProcessRepository';
import { NotifierOperationEnum } from '../../domain/data/notifier/INotifierData';

export const handler = async (event: SQSEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    data: event,
    parseData: SQSAdapter.toData,
    isSQS: true,
    schema: {
      page: yup.number().required(),
      perPage: yup.number().required(),
    },
    callback: async data => {
      const notifier = new SQSNotifierLib();
      const processRepository = new MongoProcessRepository();
      const processes = await processRepository.listProcess({
        page: data.page,
        perPage: data.perPage,
        filters: {
          hasUrl: true,
        },
      });

      await Promise.all(
        processes.processes.map(process =>
          notifier.send({
            operation: NotifierOperationEnum.GET_PROCESS_DETAILS,
            data: {
              processesNumber: process.processNumber,
            },
          }),
        ),
      );
    },
  }).build();
};
