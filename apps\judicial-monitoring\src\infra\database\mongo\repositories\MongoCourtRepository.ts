import { MongoCourtModel } from '../models/MongoCourntModel';
import * as GetCourtByName from '../../../../domain/data/courts/IGetCourtByName';
import { MongoCourtMapper } from '../mappers/MongoCourtMapper';

export class MongoCourtRepository implements GetCourtByName.IGetCourtByName {
  async execute(name: string): Promise<GetCourtByName.Result> {
    const court = await MongoCourtModel.findOne({
      name: { $regex: name, $options: 'i' },
    });
    return court ? MongoCourtMapper.toDomain(court) : null;
  }
}
