import { ProcessOriginEnum } from '../../../../constants/processOrigin';
import { ProcessEntity } from '../../../../domain/entities/ProcessEntity';
import { IMongoProcessSchema } from '../models/MongoProcessModel';

export class MongoProcessMapper {
  static toDomain(process: IMongoProcessSchema): ProcessEntity {
    return {
      id: process.id,
      externalId: process.externalId,
      court: process.court,
      processYear: process.processYear,
      processNumber: process.processNumber,
      origin: process.origin as ProcessOriginEnum,
      url: process.url,
      generalData: process.generalData,
      updates: process.updates,
      createdAt: process.createdAt,
      updatedAt: process.updatedAt,
    };
  }
}
