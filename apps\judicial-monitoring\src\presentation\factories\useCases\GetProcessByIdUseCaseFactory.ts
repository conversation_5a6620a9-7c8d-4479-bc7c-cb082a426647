import { MongoProcessRepository } from '../../../infra/database/mongo/repositories/MongoProcessRepository';
import { GetProcessByIdUseCase } from '../../../domain/useCases/GetProcessByIdUseCase';

export class GetProcessByIdUseCaseFactory extends GetProcessByIdUseCase {
  constructor() {
    const processRepository = new MongoProcessRepository();

    const dependencies = {
      getProcessById: processRepository,
    };

    super(dependencies);
  }
}
