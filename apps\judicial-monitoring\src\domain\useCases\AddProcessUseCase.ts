import { IUseCase } from '@c4u/shared';
import { IGetProcessByNumberData } from '../data/process/IGetProcessByNumberData';
import { IGetCourtByName } from '../data/courts/IGetCourtByName';
import { ProcessOriginEnum } from '../../constants/processOrigin';
import { CourtNotFoundError } from '../errors/CourtNotFoundError';
import { ISaveProcessData } from '../data/process/ISaveProcessData';
import {
  INotifierData,
  NotifierOperationEnum,
} from '../data/notifier/INotifierData';

export type Request = {
  processNumber: string;
  processYear: string;
  court: string;
  externalId: string;
  origin: ProcessOriginEnum;
};

export abstract class AddProcessUseCase implements IUseCase<Request, void> {
  constructor(
    private readonly dependencies: {
      getProcessByNumber: IGetProcessByNumberData;
      getCourtByName: IGetCourtByName;
      saveProcess: ISaveProcessData;
      notifier: INotifierData;
    },
  ) {}

  async execute({
    processNumber,
    processYear,
    court,
    origin,
    externalId,
  }: Request): Promise<void> {
    const foundCourt = await this.dependencies.getCourtByName.execute(court);

    if (!foundCourt) {
      throw new CourtNotFoundError();
    }

    const process =
      await this.dependencies.getProcessByNumber.getProcessByNumber(
        processNumber,
      );
    if (process) {
      return;
    }

    await this.dependencies.saveProcess.saveProcess({
      processNumber,
      processYear,
      court: foundCourt,
      externalId,
      origin,
    });

    await this.dependencies.notifier.send({
      operation: NotifierOperationEnum.ADDED_NEW_PROCESS,
      data: {
        processNumber,
      },
    });
  }
}
