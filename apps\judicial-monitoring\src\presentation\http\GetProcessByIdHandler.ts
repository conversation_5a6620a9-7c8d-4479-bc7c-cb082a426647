import { APIGatewayAdapter } from '@c4u/shared';
import { APIGatewayProxyEvent } from 'aws-lambda';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { GetProcessByIdUseCaseFactory } from '../factories/useCases/GetProcessByIdUseCaseFactory';

export const handler = async (event: APIGatewayProxyEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    data: event,
    parseData: APIGatewayAdapter.toData,
    schema: {
      processId: yup.string().required(),
    },
    callback: async data =>
      new GetProcessByIdUseCaseFactory().execute({
        processId: data.processId,
      }),
  }).build();
};
