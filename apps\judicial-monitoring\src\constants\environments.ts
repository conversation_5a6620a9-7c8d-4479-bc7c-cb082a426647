import { StageEnum } from '@c4u/shared';

const getStageByEnv = () => {
  switch (process.env.STAGE?.toLowerCase()) {
    case 'dev':
      return StageEnum.DEV;
    case 'prod':
      return StageEnum.PROD;
    case 'homol':
    case 'new-homol':
      return StageEnum.HOMOL;
    default:
      return StageEnum.DEV;
  }
};

export const environments = {
  STAGE: () => getStageByEnv(),
  TEAMS_WEBHOOK_URL: process.env.TEAMS_WEBHOOK_URL as string,
  MONGO_URL: process.env.MONGO_URL as string,
  AWS_ACCOUNT_ID: process.env.AWS_ACCOUNT_ID as string,
  GIUSTIZIA_IT_BASE_URL: process.env.GIUSTIZIA_IT_BASE_URL as string,
};
