import { APIGatewayEvent } from 'aws-lambda';
import { APIGatewayAdapter } from '@c4u/shared';
import * as yup from 'yup';
import { UseCaseWithErrorHandlingDecoratorFactory } from '../factories/decorators/UseCaseWithErrorHandlingDecoratorFactory';
import { GetProcessUpdatesUseCaseFactory } from '../factories/useCases/GetProcessUpdatesUseCaseFactory';

export const handler = async (event: APIGatewayEvent) => {
  return new UseCaseWithErrorHandlingDecoratorFactory({
    data: event,
    parseData: APIGatewayAdapter.toData,
    schema: {
      processNumber: yup.string().required(),
    },
    callback: async data => new GetProcessUpdatesUseCaseFactory().execute(data),
  }).build();
};
