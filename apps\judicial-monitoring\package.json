{"name": "@c4u/judicial-monitoring", "version": "1.0.0", "description": "Lambda functions for Judicial Monitoring", "scripts": {"load:env": "cd ../../packages/generate-environments && node index.js package=judicial-monitoring path=/judicial-monitoring", "lint": "eslint --ext .ts .", "start": "npm run load:env && serverless offline -s dev", "dev": "npx serverless offline"}, "dependencies": {"axios": "1.6.8", "cheerio": "^1.1.0", "dayjs": "1.11.11", "mongoose": "8.8.1", "puppeteer-core": "23.3.0"}, "devDependencies": {"@aws-sdk/client-sns": "^3.840.0", "@aws-sdk/client-sqs": "3.620.1", "@sparticuz/chromium": "127.0.0", "@types/aws-lambda": "^8.10.146", "aws-lambda": "1.0.7", "fork-ts-checker-webpack-plugin": "9.0.2", "puppeteer": "23.3.0", "serverless-apigateway-service-proxy": "2.2.1", "serverless-dotenv-plugin": "6.0.0", "serverless-lift": "1.28.1", "serverless-offline": "13.5.0", "serverless-plugin-datadog": "5.64.0", "serverless-plugin-log-retention": "2.0.0", "serverless-webpack": "5.14.2", "terser-webpack-plugin": "5.3.10", "ts-loader": "9.5.1", "typescript": "5.6.2", "webpack": "5.94.0", "webpack-node-externals": "3.0.0"}}