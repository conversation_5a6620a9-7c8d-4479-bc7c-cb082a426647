import mongoose from 'mongoose';
import { environments } from '../../../constants/environments';

mongoose.Promise = global.Promise;

let isConnected: any;

export const connectToMongoDatabase = async (): Promise<void> => {
  if (isConnected) {
    return Promise.resolve();
  }

  return mongoose
    .connect(environments.MONGO_URL, {
      dbName: 'judicial-monitoring',
    })
    .then((db: any) => {
      isConnected = db.connections[0].readyState;
    });
};
