import * as GetProcessByNumberData from '../../../../domain/data/process/IGetProcessByNumberData';
import * as SaveProcessData from '../../../../domain/data/process/ISaveProcessData';
import { MongoProcessMapper } from '../mappers/MongoProcessMapper';
import { MongoProcessModel } from '../models/MongoProcessModel';
import * as UpdateProcessData from '../../../../domain/data/process/IUpdateProcessData';
import * as ListProcessData from '../../../../domain/data/process/IListProcessData';
import * as GetProcessByIdData from '../../../../domain/data/process/IGetProcessByIdData';
import { MongoProcessHistoryModel } from '../models/MongoProcessHistoryModel';

export class MongoProcessRepository
  implements
    GetProcessByNumberData.IGetProcessByNumberData,
    SaveProcessData.ISaveProcessData,
    UpdateProcessData.IUpdateProcessData,
    ListProcessData.IListProcessData,
    GetProcessByIdData.IGetProcessByIdData
{
  async getProcessById(processId: string): Promise<GetProcessByIdData.Result> {
    const process = await MongoProcessModel.findById(processId);
    return process ? MongoProcessMapper.toDomain(process) : null;
  }

  async listProcessCount(data: ListProcessData.Params): Promise<number> {
    const { filters } = data;

    const query: any = {};

    if (filters?.hasUrl) {
      query.url = { $ne: null };
    }

    return MongoProcessModel.countDocuments(query);
  }

  async listProcess(
    data: ListProcessData.Params,
  ): Promise<ListProcessData.Result> {
    const { page = 0, perPage = 10, filters } = data;

    const query: any = {};

    if (filters?.hasUrl) {
      query.url = { $ne: null };
    }

    const processes = await MongoProcessModel.find(query)
      .skip(page * perPage)
      .limit(perPage)
      .sort({ createdAt: -1 });

    return {
      processes: processes.map(process => MongoProcessMapper.toDomain(process)),
      count: await this.listProcessCount(data),
    };
  }

  async getProcessByNumber(
    processNumber: string,
  ): Promise<GetProcessByNumberData.Result> {
    const process = await MongoProcessModel.findOne({ processNumber });
    return process ? MongoProcessMapper.toDomain(process) : null;
  }

  async saveProcess(
    data: SaveProcessData.Params,
  ): Promise<SaveProcessData.Result> {
    const process = await MongoProcessModel.create(data);
    return MongoProcessMapper.toDomain(process);
  }

  async updateProcess(
    data: UpdateProcessData.Params,
  ): Promise<UpdateProcessData.Result> {
    const session = await MongoProcessModel.startSession();
    session.startTransaction();

    try {
      const process = await MongoProcessModel.findByIdAndUpdate(data.id, data, {
        new: true,
        session,
      });

      if (!process) {
        throw new Error('MongoProcessRepository: Process not found');
      }
      const createdProcess = MongoProcessMapper.toDomain(process);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, createdAt, updatedAt, ...copyProcess } = createdProcess;

      await MongoProcessHistoryModel.create([copyProcess], {
        session,
      });

      await session.commitTransaction();
      return createdProcess;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      await session.endSession();
    }
  }
}
