import { CourtEntity } from '../../../../domain/entities/CourntEntity';
import { IMongoCourtSchema } from '../models/MongoCourntModel';

export class MongoCourtMapper {
  static toDomain(court: IMongoCourtSchema): CourtEntity {
    return {
      id: court.id,
      name: court.name,
      region: court.region,
      office: court.office,
      register: court.register,
      nameVariations: court.nameVariations,
    };
  }
}
